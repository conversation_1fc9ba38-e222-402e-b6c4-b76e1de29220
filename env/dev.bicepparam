using 'main.bicep'
param env='dev'
param project='typedapp'
param tags={env:'dev' owner:'team-infra' costCenter:'RND-001'}
param app={name:'typedapp-dev' tier:'basic' location:'eastus' ingress:{kind:'publicIp' sku:'Standard' dnsLabel:'typedapp-dev'} diagnostics:{workspaceId:null retentionDays:30}}
param vnet={name:'vnet-typedapp-dev' addressSpaces:['*********/16'] subnets:[{name:'app' prefix:'*********/24'},{name:'data' prefix:'*********/24'}]}